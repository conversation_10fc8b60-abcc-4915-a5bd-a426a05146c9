{"textures": {"name": "Текстура", "description": "Текстурные характеристики поверхности", "icon": "texture", "fields": [{"key": "value", "name": "Название текстуры", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}], "display": {"listView": ["value"], "cardView": ["value"]}, "isSimpleArray": true}, "strength_classes": {"name": "Класс прочности", "description": "Классы прочности бетонных изделий", "icon": "shield", "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[A-Z][0-9]+(\\.[0-9]+)?$", "message": "Класс должен начинаться с буквы и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"]}}, "frost_resistance": {"name": "Морозостойкость", "description": "Характеристики морозостойкости изделий", "icon": "snowflake", "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^F[0-9]+$", "message": "Класс морозостойкости должен начинаться с F и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"]}}, "water_absorption": {"name": "Водопоглощение", "description": "Характеристики водопоглощения изделий", "icon": "droplet", "fields": [{"key": "class", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^W[0-9]+$", "message": "Класс водопоглощения должен начинаться с W и содержать числа"}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["class", "description"], "cardView": ["class", "description"]}}, "standard_sizes": {"name": "Размер", "description": "Стандартные размеры изделий", "icon": "ruler", "isSimpleArray": false, "isGrouped": true, "fields": [{"key": "length", "name": "<PERSON><PERSON>ина (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}, {"key": "width", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}, {"key": "height", "name": "Высота (мм)", "type": "number", "required": true, "unique": false, "validation": {"min": 1, "max": 10000}}], "display": {"listView": ["length", "width", "height"], "cardView": ["length", "width", "height"], "colorField": "", "format": "{length}×{width}×{height} мм"}}, "surfaces": {"name": "Поверхность", "description": "Типы поверхностей изделий", "icon": "layers", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"]}}, "patterns": {"name": "Рисунок", "description": "Рисунки и узоры на изделиях", "icon": "pattern", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"]}}, "color_pigments": {"name": "Цветовые пигменты", "description": "Количество цветовых пигментов в изделии", "icon": "palette", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 100}}, {"key": "description", "name": "Описание", "type": "text", "required": true, "validation": {"minLength": 10, "maxLength": 500}}], "display": {"listView": ["name", "description"], "cardView": ["name", "description"]}}, "colors": {"name": "Цвет", "description": "Цветовые характеристики изделий", "icon": "palette", "fields": [{"key": "id", "name": "ID", "type": "string", "required": true, "unique": true, "validation": {"pattern": "^[a-z_]+$", "message": "ID должен содержать только строчные буквы и подчеркивания"}}, {"key": "name", "name": "Название", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 50}}, {"key": "hex", "name": "Hex код", "type": "color", "required": true, "validation": {"pattern": "^#[0-9A-Fa-f]{6}$", "message": "Неверный формат hex-кода цвета"}}], "display": {"listView": ["name", "hex"], "cardView": ["name", "hex"], "colorField": "hex"}}, "weight": {"name": "<PERSON>е<PERSON>", "description": "Вес изделия с указанием единицы измерения", "icon": "scale", "fields": [{"key": "value", "name": "Значение", "type": "number", "required": true, "validation": {"min": 0.01, "max": 10000}}, {"key": "unit", "name": "Единица измерения", "type": "select", "required": true, "options": [{"value": "г", "label": "грамм"}, {"value": "кг", "label": "килогра<PERSON>м"}], "validation": {"enum": ["г", "кг"]}}], "display": {"listView": ["value", "unit"], "cardView": ["value", "unit"], "format": "{value} {unit}"}}, "material": {"name": "Материал", "description": "", "icon": "", "isSimpleArray": false, "isGrouped": false, "fields": [{"key": "name", "name": "Сталь", "type": "string", "required": true, "unique": true}], "display": {"listView": [], "cardView": [], "colorField": "", "format": ""}}}